export enum CoreObjectNamePlural {
  Activity = 'activities',
  ActivityTarget = 'activityTargets',
  ApiKey = 'apiKeys',
  Attachment = 'attachments',
  Blocklist = 'blocklists',
  CalendarChannel = 'calendarChannels',
  CalendarEvent = 'calendarEvents',
  Comment = 'comments',
  Company = 'companies',
  ConnectedAccount = 'connectedAccounts',
  TimelineActivity = 'timelineActivities',
  Favorite = 'favorites',
  Message = 'messages',
  MessageChannel = 'messageChannels',
  MessageParticipant = 'messageParticipants',
  MessageThread = 'messageThreads',
  Note = 'notes',
  NoteTarget = 'noteTargets',
  Opportunity = 'opportunities',
  Person = 'people',
  Task = 'tasks',
  TaskTarget = 'taskTargets',
  View = 'views',
  ViewField = 'viewFields',
  ViewFilter = 'viewFilters',
  ViewFilterGroup = 'viewFilterGroups',
  ViewSort = 'viewSorts',
  ViewGroup = 'viewGroups',
  Webhook = 'webhooks',
  WorkspaceMember = 'workspaceMembers',
  MessageThreadSubscriber = 'messageThreadSubscribers',
  Workflow = 'workflows',
  MessageChannelMessageAssociation = 'messageChannelMessageAssociations',
  WorkflowVersion = 'workflowVersions',
  WorkflowRun = 'workflowRuns',
}
