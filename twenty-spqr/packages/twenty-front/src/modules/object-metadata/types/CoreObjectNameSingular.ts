export enum CoreObjectNameSingular {
  Activity = 'activity',
  ActivityTarget = 'activityTarget',
  ApiKey = 'apiKey',
  Attachment = 'attachment',
  Blocklist = 'blocklist',
  CalendarChannel = 'calendarChannel',
  CalendarEvent = 'calendarEvent',
  Comment = 'comment',
  Company = 'company',
  ConnectedAccount = 'connectedAccount',
  TimelineActivity = 'timelineActivity',
  Favorite = 'favorite',
  FavoriteFolder = 'favoriteFolder',
  Message = 'message',
  MessageChannel = 'messageChannel',
  MessageParticipant = 'messageParticipant',
  MessageThread = 'messageThread',
  Note = 'note',
  NoteTarget = 'noteTarget',
  Opportunity = 'opportunity',
  Person = 'person',
  Task = 'task',
  TaskTarget = 'taskTarget',
  View = 'view',
  ViewField = 'viewField',
  ViewFilter = 'viewFilter',
  ViewFilterGroup = 'viewFilterGroup',
  ViewSort = 'viewSort',
  ViewGroup = 'viewGroup',
  Webhook = 'webhook',
  WorkspaceMember = 'workspaceMember',
  MessageThreadSubscriber = 'messageThreadSubscriber',
  Workflow = 'workflow',
  MessageChannelMessageAssociation = 'messageChannelMessageAssociation',
  WorkflowVersion = 'workflowVersion',
  WorkflowRun = 'workflowRun',
}
